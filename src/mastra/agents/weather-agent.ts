import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { Agent } from "@mastra/core/agent";
import { weatherTool } from "../tools/weather-tool";
 
const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

const llm = openrouter("anthropic/claude-sonnet-4");

export const weatherAgent = new Agent({
  name: "Weather Agent",
  instructions: `You are a helpful weather assistant that provides accurate weather information.
 
Your primary function is to help users get weather details for specific locations. When responding:
- Always ask for a location if none is provided
- If the location name isn’t in English, please translate it
- Include relevant details like humidity, wind conditions, and precipitation
- Keep responses concise but informative
 
Use the weatherTool to fetch current weather data.`,
  model: llm,
  tools: {
    weather: weatherTool,
  },
});