import { <PERSON><PERSON> } from "@mastra/core";
import { <PERSON><PERSON>Logger } from "@mastra/loggers";
import { UpstashStore } from "@mastra/upstash";
import { weatherAgent } from "./agents/weather-agent";
import { weatherWorkflow } from "./workflows/weather-workflow";

const storage = new UpstashStore({
	url: process.env.UPSTASH_URL as string,
	token: process.env.UPSTASH_TOKEN as string,
});

export const mastra = new Mastra({
	workflows: { weatherWorkflow },
	agents: { weatherAgent },
	storage,
	server: {
		cors: false,
	},
	logger: new PinoLogger({
		name: "<PERSON><PERSON>",
		level: "info",
	}),
});
