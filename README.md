# DeFi Agent Server

A Mastra-powered DeFi agent with integrated MCP (Model Context Protocol) servers for cryptocurrency analysis and trading insights.

## Features

- **Weather Agent**: Basic weather information (example)
- **Crypto Sentiment Analysis**: Market sentiment using Santiment API
- **Fear and Greed Index**: Cryptocurrency market sentiment indicator
- **Technical Indicators**: Price data and technical analysis from Binance
- **MCP Integration**: Modular tools via Model Context Protocol

## Installation

Install dependencies:

```bash
bun install
```

## Configuration

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Configure your API keys in `.env`:
```bash
# Required for sentiment analysis
SANTIMENT_API_KEY=your_santiment_api_key_here

# Storage configuration
UPSTASH_URL=your_upstash_url_here
UPSTASH_TOKEN=your_upstash_token_here
```

## Running the Server

Development mode:
```bash
bun run dev
```

Production build:
```bash
bun run build
```

Direct execution:
```bash
bun run index.ts
```

## MCP Servers

This project includes three MCP servers for crypto functionality:

1. **crypto-sentiment-mcp**: Sentiment analysis using Santiment API
2. **crypto-feargreed-mcp**: Fear and greed index data
3. **crypto-indicators-mcp**: Technical indicators using Binance data

See `src/mastra/mcp/README.md` for detailed documentation.

## Project Structure

```
src/mastra/
├── agents/           # Mastra agents
├── tools/           # Custom tools
├── workflows/       # Mastra workflows
├── mcp/            # MCP servers and tools
│   ├── crypto-sentiment-mcp/
│   ├── crypto-feargreed-mcp/
│   └── crypto-indicators-mcp/
└── index.ts        # Main Mastra configuration
```

## Available Tools

- Weather information
- Crypto sentiment analysis
- Market fear and greed index
- Price data from exchanges
- Technical indicators (RSI, MACD, etc.)
- Trading volume analysis
- Order book depth data

## Technology Stack

- **Runtime**: [Bun](https://bun.sh) - Fast all-in-one JavaScript runtime
- **Framework**: [Mastra](https://mastra.ai) - AI agent framework
- **Protocol**: [MCP](https://modelcontextprotocol.io) - Model Context Protocol
- **Storage**: Upstash Redis
- **APIs**: Santiment, Binance, Alternative.me
